
"""
Centralized configuration for the pivot point strategy application.
Contains all constants, default values, and configuration settings.
"""
import os
import sys
import time
import subprocess
import webbrowser
import logging
from datetime import datetime
from dotenv import load_dotenv

# Configure logging
def setup_logging(level=logging.INFO, log_to_file=True, reset=False):
    """
    Setup logging configuration with optional file logging and rotation

    Parameters:
        level: Logging level (default: logging.INFO)
        log_to_file: Whether to log to a file (default: True)
        reset: Whether to reset all loggers (default: False)

    Returns:
        Logger instance
    """
    # Create logs directory if it doesn't exist
    if log_to_file and not os.path.exists('logs'):
        try:
            os.makedirs('logs')
        except Exception as e:
            logger.warning(f"Could not create logs directory: {e}")
            log_to_file = False

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # Reset all loggers if requested
    if reset:
        # Remove all handlers from all loggers
        for logger_name in logging.root.manager.loggerDict:
            logger_obj = logging.getLogger(logger_name)
            for handler in logger_obj.handlers[:]:
                logger_obj.removeHandler(handler)

        # Remove all handlers from root logger
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
    else:
        # Just remove existing handlers from root logger to avoid duplicates
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    # Add file handler with rotation if requested
    if log_to_file:
        try:
            from logging.handlers import RotatingFileHandler

            # Get current date for log filename
            current_date = datetime.now().strftime('%Y%m%d')
            log_file = f'logs/pivot_strategy_{current_date}.log'

            # Create rotating file handler (10 MB max size, keep 5 backup files)
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=10*1024*1024,  # 10 MB
                backupCount=5
            )
            file_handler.setLevel(level)
            file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)

            # Log the start of a new session
            root_logger.info("=" * 80)
            root_logger.info("Starting new logging session")
            root_logger.info("=" * 80)
        except Exception as e:
            logger.warning(f"Could not set up file logging: {e}")

    # Return logger for the current module
    return logging.getLogger(__name__)

# Default paths
DEFAULT_ENV_PATH = "C:/Users/<USER>/Desktop/Python/.env"
DEFAULT_OUTPUT_DIR = "reports"

# Load environment variables
def load_environment(env_path=None):
    """Load environment variables from .env file"""
    if env_path is None:
        env_path = DEFAULT_ENV_PATH

    if os.path.exists(env_path):
        load_dotenv(env_path)
        return True
    return False

# Fyers API configuration
class FyersConfig:
    """Configuration and authentication for Fyers API"""

    # Default configuration
    DEFAULT_CONFIG = {
        "redirect_uri": "https://www.google.co.in/",
        "client_id": "N54YYD66HI-100",
        "secret_key": "2F4BVWGL3G",
        "grant_type": "authorization_code",
        "response_type": "code",
        "state": "sample"
    }

    # Browser configurations
    WINDOWS_BROWSERS = [
        (r'C:\Program Files\Google Chrome\Application\chrome.exe', '--incognito'),
        (r'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe', '--inprivate'),
        (r'C:\Users\<USER>\AppData\Local\Programs\Opera\opera.exe', '--private'),
        (r'C:\Program Files\Opera\opera.exe', '--private'),
        (r'C:\Program Files (x86)\Opera\opera.exe', '--private')
    ]

    UNIX_BROWSERS = [
        ('/usr/bin/google-chrome', '--incognito'),
        ('/usr/bin/firefox', '--private-window'),
        ('/usr/bin/microsoft-edge', '--inprivate'),
        ('/usr/bin/opera', '--private')
    ]

    def __init__(self, env_path=None, auth_dir=None):
        """Initialize Fyers configuration"""
        # Load environment variables
        load_environment(env_path)

        # Set authentication directory
        self.auth_dir = auth_dir or os.path.dirname(os.path.abspath(__file__))
        if not os.path.exists(self.auth_dir):
            os.makedirs(self.auth_dir)

        # Set file paths
        self.client_id_file = os.path.join(self.auth_dir, "fyers_client_id.txt")
        self.access_token_file = os.path.join(self.auth_dir, "fyers_access_token.txt")

        # Load credentials from environment variables or use defaults
        self.config = self.DEFAULT_CONFIG.copy()
        self.config.update({
            "client_id": os.getenv('FYERS_CLIENT_ID', self.config["client_id"]),
            "secret_key": os.getenv('FYERS_SECRET_KEY', self.config["secret_key"]),
            "redirect_uri": os.getenv('FYERS_REDIRECT_URI', self.config["redirect_uri"])
        })

    def authenticate(self):
        """Run the Fyers authentication process and return the access token."""
        try:
            logger = logging.getLogger(__name__)

            # First check if we have existing valid tokens from today
            if os.path.exists(self.access_token_file) and os.path.exists(self.client_id_file):
                token_mtime = datetime.fromtimestamp(os.path.getmtime(self.access_token_file))
                if token_mtime.date() == datetime.now().date():
                    
                    with open(self.access_token_file, 'r') as f:
                        access_token = f.read().strip()

                    if self._verify_connection(access_token):
                        
                        return access_token
                    else:
                        logger.warning("Existing tokens are invalid, will get new ones")

            

            from fyers_apiv3 import fyersModel
            appSession = fyersModel.SessionModel(
                client_id=self.config["client_id"],
                redirect_uri=self.config["redirect_uri"],
                response_type=self.config["response_type"],
                state=self.config["state"],
                secret_key=self.config["secret_key"],
                grant_type=self.config["grant_type"]
            )

            generateTokenUrl = appSession.generate_authcode()
            self._open_browser(generateTokenUrl)
            auth_code = self._get_auth_code()
            if not auth_code:
                return None

            appSession.set_token(auth_code)
            token_response = appSession.generate_token()

            if not token_response or not token_response.get("access_token"):
                logger.error(f"Failed to generate access token: {token_response}")
                return None
            
            access_token = token_response["access_token"]
            
            self._save_tokens(self.config["client_id"], access_token)

            if self._verify_connection(access_token):
                return access_token
            else:
                return None

        except Exception as e:
            logger.error(f"Error during Fyers authentication: {str(e)}")
            return None

    def _open_browser(self, url):
        """Open browser in private/incognito mode"""
        try:
            if sys.platform.startswith('win'):
                self._open_windows_browser(url)
            else:
                self._open_unix_browser(url)
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.warning(f"Failed to open browser in private mode: {str(e)}")
            webbrowser.open(url, new=2)

    def _open_windows_browser(self, url):
        """Open browser in Windows"""
        logger = logging.getLogger(__name__)
        username = os.getenv('USERNAME')
        browsers = [(path.replace('%USERNAME%', username), flag)
                   if '%USERNAME%' in path else (path, flag)
                   for path, flag in self.WINDOWS_BROWSERS]

        for browser_path, private_flag in browsers:
            if os.path.exists(browser_path):
                subprocess.Popen([browser_path, private_flag, url])
                # logger.info(f"Opening {os.path.basename(browser_path)} in private mode")
                return True
        return False

    def _open_unix_browser(self, url):
        """Open browser in Unix-like systems"""
        logger = logging.getLogger(__name__)
        for browser_path, private_flag in self.UNIX_BROWSERS:
            if os.path.exists(browser_path):
                subprocess.Popen([browser_path, private_flag, url])
                # logger.info(f"Opening {os.path.basename(browser_path)} in private mode")
                return True
        return False

    def _get_auth_code(self):
        """Get authentication code from user"""
        logger = logging.getLogger(__name__)
        logger.info("\n=== Fyers API Authentication ===")
        logger.info("A browser window will open for you to log in to Fyers.")
        logger.info("After logging in, you will be redirected to Google.")
        logger.info("Copy the auth code from the URL and paste it here.")
        logger.info("\nPlease login in the private browser window that opened.")
        time.sleep(2)

        auth_code = input("\nEnter Auth Code from the redirected URL: ")
        if not auth_code:
            logger.error("No auth code provided")
            return None
        return auth_code

    def _save_tokens(self, client_id, access_token):
        """Save authentication tokens to files"""
        logger = logging.getLogger(__name__)
        with open(self.client_id_file, 'w') as file:
            file.write(client_id)
        with open(self.access_token_file, 'w') as file:
            file.write(access_token)
        logger.info(f"Authentication files saved to {self.auth_dir}")

    def _verify_connection(self, access_token):
        """Verify Fyers connection with access token"""
        try:
            logger = logging.getLogger(__name__)
            from fyers_apiv3.fyersModel import FyersModel

            # Create API instance
            fyers = FyersModel(
                client_id=self.config["client_id"],
                is_async=False,
                token=access_token
            )

            # Test the connection
            profile = fyers.get_profile()
            if profile and profile.get("code") == 200:
                logger.info("Fyers authentication successful!")
                return True
            else:
                logger.error(f"Failed to verify Fyers connection: {profile}")
                return False

        except Exception as e:
            logger.error(f"Error verifying connection: {str(e)}")
            return False
