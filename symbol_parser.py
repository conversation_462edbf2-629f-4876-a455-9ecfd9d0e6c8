"""
Symbol parser for extracting NIFTY and BANKNIFTY options symbols from NSE_FO.csv.
Filters symbols for current month, next month, and next-next month.
"""

import csv
import logging
import re
from datetime import datetime, timedelta
from typing import List, Dict, Set
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class OptionSymbol:
    """Data class to represent an option symbol."""
    symbol: str
    underlying: str
    expiry_date: str
    strike_price: float
    option_type: str  # CE or PE
    full_symbol: str  # NSE:NIFTY25JUL57000CE format

    @property
    def strike(self) -> float:
        """Alias for strike_price for backward compatibility."""
        return self.strike_price

class SymbolParser:
    """Parser for extracting and filtering option symbols from NSE_FO.csv."""
    
    def __init__(self, csv_file_path: str = "NSE_FO.csv"):
        """
        Initialize the symbol parser.
        
        Args:
            csv_file_path: Path to the NSE_FO.csv file
        """
        self.csv_file_path = csv_file_path
        self.target_symbols = {'NIFTY', 'BANKNIFTY'}
        
    def get_target_months(self) -> List[str]:
        """
        Get the target months for filtering: current month, next month, next-next month.
        
        Returns:
            List of month abbreviations in format like ['JUL', 'AUG', 'SEP']
        """
        current_date = datetime.now()
        months = []
        
        for i in range(3):  # Current month + next 2 months
            target_date = current_date + timedelta(days=30 * i)
            month_abbr = target_date.strftime('%b').upper()
            months.append(month_abbr)
        
        # Remove duplicates while preserving order
        unique_months = []
        for month in months:
            if month not in unique_months:
                unique_months.append(month)
                
        logger.info(f"Target months for filtering: {unique_months}")
        return unique_months
    
    def parse_symbol_from_nse_format(self, nse_symbol: str) -> OptionSymbol:
        """
        Parse NSE format symbol like 'NSE:NIFTY25JUL57000CE' into components.
        
        Args:
            nse_symbol: Symbol in NSE format
            
        Returns:
            OptionSymbol object or None if parsing fails
        """
        try:
            # Remove NSE: prefix if present
            symbol = nse_symbol.replace('NSE:', '')
            
            # Pattern to match: UNDERLYING + YY + MMM + STRIKE + CE/PE
            # Example: NIFTY25JUL57000CE, BANKNIFTY25SEP25500PE
            pattern = r'^(NIFTY|BANKNIFTY|FINNIFTY)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$'
            match = re.match(pattern, symbol)
            
            if not match:
                return None
                
            underlying = match.group(1)
            year = match.group(2)
            month = match.group(3)
            strike = float(match.group(4))
            option_type = match.group(5)
            
            # Create expiry date string
            expiry_date = f"{year}{month}"
            
            return OptionSymbol(
                symbol=symbol,
                underlying=underlying,
                expiry_date=expiry_date,
                strike_price=strike,
                option_type=option_type,
                full_symbol=nse_symbol
            )
            
        except Exception as e:
            logger.debug(f"Failed to parse symbol {nse_symbol}: {e}")
            return None
    
    def load_symbols_from_csv(self) -> List[OptionSymbol]:
        """
        Load and parse symbols from NSE_FO.csv file.
        
        Returns:
            List of OptionSymbol objects
        """
        symbols = []
        target_months = self.get_target_months()
        
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)
                
                for row_num, row in enumerate(csv_reader, 1):
                    try:
                        # Column J (index 9) contains the NSE symbol
                        if len(row) > 9:
                            nse_symbol = row[9].strip()
                            
                            # Parse the symbol
                            parsed_symbol = self.parse_symbol_from_nse_format(nse_symbol)
                            
                            if parsed_symbol:
                                # Filter for target symbols (NIFTY, BANKNIFTY)
                                if parsed_symbol.underlying in self.target_symbols:
                                    # Extract month from expiry date (last 3 characters)
                                    month_part = parsed_symbol.expiry_date[-3:]
                                    
                                    # Filter for target months
                                    if month_part in target_months:
                                        symbols.append(parsed_symbol)
                                        
                    except Exception as e:
                        logger.debug(f"Error processing row {row_num}: {e}")
                        continue
                        
        except FileNotFoundError:
            logger.error(f"CSV file not found: {self.csv_file_path}")
            raise
        except Exception as e:
            logger.error(f"Error reading CSV file: {e}")
            raise
            
        logger.info(f"Loaded {len(symbols)} symbols from CSV for target months")
        return symbols
    
    def filter_symbols_by_underlying(self, symbols: List[OptionSymbol], 
                                   underlying_symbols: List[str]) -> List[OptionSymbol]:
        """
        Filter symbols by underlying symbols.
        
        Args:
            symbols: List of OptionSymbol objects
            underlying_symbols: List of underlying symbols to filter by
            
        Returns:
            Filtered list of OptionSymbol objects
        """
        filtered = [s for s in symbols if s.underlying in underlying_symbols]
        logger.info(f"Filtered to {len(filtered)} symbols for underlyings: {underlying_symbols}")
        return filtered
    
    def get_symbols_for_scanning(self, underlying_symbols: List[str] = None) -> List[str]:
        """
        Get list of symbols ready for market data scanning.
        
        Args:
            underlying_symbols: List of underlying symbols to filter by (default: NIFTY, BANKNIFTY)
            
        Returns:
            List of symbol strings in NSE format
        """
        if underlying_symbols is None:
            underlying_symbols = list(self.target_symbols)
            
        # Load symbols from CSV
        all_symbols = self.load_symbols_from_csv()
        
        # Filter by underlying symbols
        filtered_symbols = self.filter_symbols_by_underlying(all_symbols, underlying_symbols)
        
        # Extract full symbol strings
        symbol_strings = [s.full_symbol for s in filtered_symbols]
        
        logger.info(f"Prepared {len(symbol_strings)} symbols for scanning")
        return symbol_strings
    
    def get_symbol_details(self, underlying_symbols: List[str] = None) -> List[OptionSymbol]:
        """
        Get detailed symbol information for analysis.
        
        Args:
            underlying_symbols: List of underlying symbols to filter by
            
        Returns:
            List of OptionSymbol objects with detailed information
        """
        if underlying_symbols is None:
            underlying_symbols = list(self.target_symbols)
            
        # Load symbols from CSV
        all_symbols = self.load_symbols_from_csv()
        
        # Filter by underlying symbols
        filtered_symbols = self.filter_symbols_by_underlying(all_symbols, underlying_symbols)
        
        return filtered_symbols
