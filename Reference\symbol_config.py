"""
Configuration settings for different trading symbols.
"""

# Symbol-specific configuration
SYMBOL_CONFIG = {
    # Index configurations
    "NIFTY": {
        "strike_interval": 50,  # Strike prices increment by 50 points
        "distance_threshold_pct": 0.5,  # 0.5% distance from pivot
        "min_volume": 1000,
        "min_price": 2.0,
        "target_delta": 0.5,
        "num_strikes_each_side": 10 # Number of strikes above and below spot
    },
    "BANKNIFTY": {
        "strike_interval": 100,  # Strike prices increment by 100 points
        "distance_threshold_pct": 0.75,  # 0.75% distance from pivot (increased for wider intervals)
        "min_volume": 500,  # Adjusted for potentially lower volume
        "min_price": 5.0,  # Adjusted for typically higher premiums
        "target_delta": 0.5,
        "num_strikes_each_side": 10
    },
    "FINNIFTY": {
        "strike_interval": 50,  # Strike prices increment by 50 points
        "distance_threshold_pct": 0.5,
        "min_volume": 500,
        "min_price": 2.0,
        "target_delta": 0.5,
        "num_strikes_each_side": 10
    },

    # Stock configurations
    "RELIANCE": {
        "strike_interval": 20,  # Strike prices increment by 20 points
        "distance_threshold_pct": 1.0,  # 1.0% distance from pivot (stocks can be more volatile)
        "min_volume": 100,  # Lower volume for stock options
        "min_price": 1.0,  # Lower minimum price for stock options
        "target_delta": 0.5,
        "num_strikes_each_side": 7 # Smaller range for stocks
    },
    "HDFCBANK": {
        "strike_interval": 20,
        "distance_threshold_pct": 1.0,
        "min_volume": 100,
        "min_price": 1.0,
        "target_delta": 0.5,
        "num_strikes_each_side": 7
    },
    "INFY": {
        "strike_interval": 20,
        "distance_threshold_pct": 1.0,
        "min_volume": 100,
        "min_price": 1.0,
        "target_delta": 0.5,
        "num_strikes_each_side": 7
    },
    "TCS": {
        "strike_interval": 20,
        "distance_threshold_pct": 1.0,
        "min_volume": 100,
        "min_price": 1.0,
        "target_delta": 0.5,
        "num_strikes_each_side": 7
    },
    "WIPRO": {
        "strike_interval": 10,  # Smaller strike interval for lower-priced stocks
        "distance_threshold_pct": 1.0,
        "min_volume": 100,
        "min_price": 0.5,
        "target_delta": 0.5,
        "num_strikes_each_side": 7
    },

    # Default configuration for any other symbol
    "DEFAULT": {
        "strike_interval": 20,  # Default to 20-point intervals for stocks
        "distance_threshold_pct": 1.0,  # Default to 1% for stocks
        "min_volume": 100,
        "min_price": 1.0,
        "target_delta": 0.5,
        "num_strikes_each_side": 7 # Default smaller range
    }
}

def get_dynamic_symbol_config(symbol, fyers_instance):
    """
    Dynamically generate config for a symbol based on its spot price.
    """
    import logging
    spot_price = 0
    try:
        # Fetch spot price using FyersConnect instance
        spot_price = fyers_instance.get_spot_price(symbol) if fyers_instance else 0
    except Exception as e:
        logging.warning(f"Could not fetch spot price for {symbol}: {e}")

    # Generalize strike interval
    if spot_price < 500: # Adjusted range for 10-point interval
        strike_interval = 5
    elif spot_price < 1000:
        strike_interval = 20
    elif spot_price < 2000:
        strike_interval = 50
    else:
        strike_interval = 100

    min_price = max(1.0, spot_price * 0.005)
    min_volume = 100 if spot_price < 1000 else 50
    target_delta = 0.5

    return {
        "strike_interval": strike_interval,
        "distance_threshold_pct": 1.0,
        "min_volume": min_volume,
        "min_price": min_price,
        "target_delta": target_delta
    }

def get_symbol_config(symbol, fyers_instance=None):
    """
    Get configuration for a specific symbol. If not found, generate dynamically using spot price.

    Parameters:
        symbol: Trading symbol (e.g., 'NIFTY', 'BANKNIFTY')
        fyers_instance: Optional FyersConnect instance for spot price lookup

    Returns:
        Dictionary with configuration values for the symbol
    """
    import logging
    symbol = symbol.upper()
    if symbol in SYMBOL_CONFIG:
        return SYMBOL_CONFIG[symbol]
    elif fyers_instance is not None:
        logging.info(f"Generating config for new symbol '{symbol}' based on spot price.")
        return get_dynamic_symbol_config(symbol, fyers_instance)
    else:
        logging.warning(f"Symbol '{symbol}' not found and no fyers_instance provided. Using DEFAULT config.")
        return SYMBOL_CONFIG["DEFAULT"]
