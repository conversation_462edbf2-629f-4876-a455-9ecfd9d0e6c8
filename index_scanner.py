"""
Index Scanner core logic for filtering NIFTY and BANKNIFTY options.
Applies volume and LTP filters based on configuration.
"""

import logging
from typing import List, Dict, Tuple
from dataclasses import dataclass
from datetime import datetime
import re

from config_loader import ConfigLoader
from symbol_parser import SymbolParser, OptionSymbol
from fyers_client import FyersClient, MarketData

logger = logging.getLogger(__name__)

@dataclass
class FilteredSymbol:
    """Data class for filtered symbol with market data."""
    strike: float
    expiry_date: str
    option_type: str  # CE or PE
    symbol: str
    ltp: float
    volume: int
    open_price: float
    high: float
    low: float
    close: float
    prev_close: float
    change: float
    change_percent: float

class IndexScanner:
    """Main index scanner for filtering options based on criteria."""
    
    def __init__(self, config: ConfigLoader):
        """
        Initialize the index scanner.
        
        Args:
            config: Configuration loader instance
        """
        self.config = config
        self.symbol_parser = SymbolParser()
        self.fyers_client = FyersClient(env_path=config.env_path)
        
    def authenticate_fyers(self) -> bool:
        """
        Authenticate with Fyers API.
        
        Returns:
            True if authentication successful, False otherwise
        """
        logger.info("Authenticating with Fyers API...")
        return self.fyers_client.authenticate()
    
    def extract_expiry_info(self, symbol_str: str) -> Tuple[str, str]:
        """
        Extract expiry date and format it for display.
        
        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'
            
        Returns:
            Tuple of (formatted_expiry_date, option_type)
        """
        try:
            # Remove NSE: prefix
            clean_symbol = symbol_str.replace('NSE:', '')
            
            # Extract components using regex
            pattern = r'^(NIFTY|BANKNIFTY)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$'
            match = re.match(pattern, clean_symbol)
            
            if match:
                year = match.group(2)
                month = match.group(3)
                option_type = match.group(5)
                
                # Format as readable date
                expiry_date = f"20{year}-{month}"
                return expiry_date, option_type
            else:
                return "Unknown", "Unknown"
                
        except Exception as e:
            logger.debug(f"Error extracting expiry info from {symbol_str}: {e}")
            return "Unknown", "Unknown"
    
    def extract_strike_price(self, symbol_str: str) -> float:
        """
        Extract strike price from symbol string.
        
        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'
            
        Returns:
            Strike price as float
        """
        try:
            # Remove NSE: prefix
            clean_symbol = symbol_str.replace('NSE:', '')
            
            # Extract strike price using regex
            pattern = r'^(NIFTY|BANKNIFTY)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$'
            match = re.match(pattern, clean_symbol)
            
            if match:
                return float(match.group(4))
            else:
                return 0.0
                
        except Exception as e:
            logger.debug(f"Error extracting strike price from {symbol_str}: {e}")
            return 0.0
    
    def apply_filters(self, market_data: Dict[str, MarketData]) -> List[FilteredSymbol]:
        """
        Apply volume and LTP filters to market data.
        
        Args:
            market_data: Dictionary of symbol to MarketData
            
        Returns:
            List of FilteredSymbol objects that pass the filters
        """
        filtered_symbols = []
        
        min_volume = self.config.min_volume
        min_ltp = self.config.min_ltp_price
        max_ltp = self.config.max_ltp_price
        
        logger.info(f"Applying filters - Min Volume: {min_volume}, LTP Range: {min_ltp}-{max_ltp}")
        
        for symbol, data in market_data.items():
            try:
                # Apply volume filter
                if data.volume < min_volume:
                    continue
                
                # Apply LTP filter
                if data.ltp < min_ltp or data.ltp > max_ltp:
                    continue
                
                # Extract additional information
                strike_price = self.extract_strike_price(symbol)
                expiry_date, option_type = self.extract_expiry_info(symbol)
                
                # Create filtered symbol
                filtered_symbol = FilteredSymbol(
                    strike=strike_price,
                    expiry_date=expiry_date,
                    option_type=option_type,
                    symbol=symbol,
                    ltp=data.ltp,
                    volume=data.volume,
                    open_price=data.open_price,
                    high=data.high,
                    low=data.low,
                    close=data.close,
                    prev_close=data.prev_close,
                    change=data.change,
                    change_percent=data.change_percent
                )
                
                filtered_symbols.append(filtered_symbol)
                
            except Exception as e:
                logger.warning(f"Error processing symbol {symbol}: {e}")
                continue
        
        logger.info(f"Filtered {len(filtered_symbols)} symbols from {len(market_data)} total symbols")
        return filtered_symbols
    
    def scan_symbols(self) -> List[FilteredSymbol]:
        """
        Main scanning method that orchestrates the entire process.
        
        Returns:
            List of FilteredSymbol objects that pass all filters
        """
        try:
            logger.info("Starting index scanner...")
            
            # Step 1: Authenticate with Fyers
            if not self.authenticate_fyers():
                logger.error("Failed to authenticate with Fyers API")
                return []
            
            # Step 2: Get symbols for scanning
            logger.info("Loading symbols from CSV...")
            symbols_to_scan = self.symbol_parser.get_symbols_for_scanning(
                underlying_symbols=self.config.symbols
            )
            
            if not symbols_to_scan:
                logger.warning("No symbols found for scanning")
                return []
            
            logger.info(f"Found {len(symbols_to_scan)} symbols to scan")
            
            # Step 3: Fetch market data
            logger.info("Fetching market data from Fyers API...")
            market_data = self.fyers_client.get_quotes(symbols_to_scan)
            
            if not market_data:
                logger.warning("No market data received")
                return []
            
            logger.info(f"Received market data for {len(market_data)} symbols")
            
            # Step 4: Apply filters
            logger.info("Applying filters...")
            filtered_symbols = self.apply_filters(market_data)
            
            logger.info(f"Scanning complete. Found {len(filtered_symbols)} symbols matching criteria")
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Error during scanning: {e}")
            return []
    
    def get_scan_summary(self, filtered_symbols: List[FilteredSymbol]) -> Dict[str, int]:
        """
        Get summary statistics of the scan results.
        
        Args:
            filtered_symbols: List of filtered symbols
            
        Returns:
            Dictionary with summary statistics
        """
        summary = {
            'total_symbols': len(filtered_symbols),
            'nifty_symbols': 0,
            'banknifty_symbols': 0,
            'ce_options': 0,
            'pe_options': 0
        }
        
        for symbol in filtered_symbols:
            if 'NIFTY' in symbol.symbol and 'BANKNIFTY' not in symbol.symbol:
                summary['nifty_symbols'] += 1
            elif 'BANKNIFTY' in symbol.symbol:
                summary['banknifty_symbols'] += 1
                
            if symbol.option_type == 'CE':
                summary['ce_options'] += 1
            elif symbol.option_type == 'PE':
                summary['pe_options'] += 1
        
        return summary
