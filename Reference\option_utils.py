"""
General utility functions for option chain processing.
"""
import logging
import numpy as np
from datetime import datetime, date, timedelta
import math
from scipy.stats import norm
from scipy.optimize import brentq

# Configure logging
logger = logging.getLogger(__name__)

# Date and Expiry Helper Functions
def get_last_thursday_of_month(year, month):
    """
    Get the last Thursday of a given month
    
    Parameters:
        year: int - Year
        month: int - Month (1-12)
        
    Returns:
        date - Last Thursday of the month
    """
    # Get the last day of the month
    if month == 12:
        last_day = date(year, month, 31)
    else:
        last_day = date(year, month + 1, 1) - timedelta(days=1)
    
    # Get the last Thursday of the month
    offset = (last_day.weekday() - 3) % 7
    last_thursday = last_day - timedelta(days=offset)
    return last_thursday

def format_weekly_option_symbol(fyers_connect_instance, base_symbol: str, expiry_date: datetime, strike: float, option_type: str) -> str:
    """
    Format weekly option symbol for Fyers API.
    
    Args:
        base_symbol: Base symbol (e.g., 'NIFTY', 'BANKNIFTY')
        expiry_date: Expiry date
        strike: Strike price
        option_type: Option type ('CE' or 'PE')
        
    Returns:
        str: Formatted option symbol
    """
    # Format: NSE:NIFTYYYMDD19000CE
    # YY = year (2 digits)
    # M = month (single digit)
    # DD = day (2 digits)
    year = expiry_date.strftime('%y')
    month = str(expiry_date.month)  # Single digit month
    day = expiry_date.strftime('%d')
    strike_int = int(strike)
    
    # Correct format for Fyers API
    exchange = fyers_connect_instance._get_exchange_for_symbol(base_symbol)
    symbol = f"{exchange}:{base_symbol}{year}{month}{day}{strike_int}{option_type}"
    
    return symbol

def format_monthly_option_symbol(fyers_connect_instance, base_symbol: str, expiry_date: datetime, strike: float, option_type: str) -> str:
    """
    Format monthly option symbol for Fyers API (e.g., NSE:NIFTY25JUL25500CE, NSE:RELIANCE25JUL1680PE).
    """
    exchange = fyers_connect_instance._get_exchange_for_symbol(base_symbol)
    year = expiry_date.strftime('%y')
    month_abbr = expiry_date.strftime('%b').upper()  # 3-letter month (e.g., JUL)
    strike_int = int(strike)
    
    symbol = f"{exchange}:{base_symbol}{year}{month_abbr}{strike_int}{option_type}"
    return symbol

# --- Black-Scholes Option Pricing and Greeks ---
# --- General Black-Scholes Option Price for CE and PE ---
def black_scholes_price(S, K, T, r, sigma, option_type):
    sqrtT = np.sqrt(T)
    lnSK = np.log(S / K)
    d1 = (lnSK + (r + 0.5 * sigma ** 2) * T) / (sigma * sqrtT)
    d2 = d1 - sigma * sqrtT

    if option_type.upper() == 'CE':
        return S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
    elif option_type.upper() == 'PE':
        return K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
    else:
        raise ValueError("option_type must be 'CE' or 'PE'")

# --- Implied Volatility Solver ---
def implied_volatility(symbol, price, S, K, T, r, option_type):
    def objective(sigma):
        return black_scholes_price(S, K, T, r, sigma, option_type) - price
    try:
        # Ensure T is not zero or negative for brentq
        if T <= 0:
            return 0.0001 # Return a small positive value if T is invalid
        return brentq(objective, 1e-5, 5.0)
    except ValueError as e:
        logger.warning(f"Could not find implied volatility for symbol={symbol}, price={price}, S={S}, K={K}, T={T}, r={r}, option_type={option_type}: {e}. Returning default IV.")
        return 0.2 # Return a default implied volatility (e.g., 20%) if brentq fails

# --- Delta Calculator (Retained Original Name) ---
def black_scholes_delta(S, K, T, r, sigma, option_type):
    logger.debug(f"BS Delta Inputs: S={S}, K={K}, T={T}, r={r}, sigma={sigma}, type={option_type}")
    if T <= 0 or sigma <= 0 or np.isnan(sigma):
        logger.warning(f"Invalid T or sigma for Black-Scholes Delta. T={T}, sigma={sigma}. Returning 0.0")
        return 0.0

    d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))

    if option_type.upper() == 'CE':
        return norm.cdf(d1)
    elif option_type.upper() == 'PE':
        return norm.cdf(d1) - 1
    else:
        raise ValueError("option_type must be 'CE' or 'PE'")
